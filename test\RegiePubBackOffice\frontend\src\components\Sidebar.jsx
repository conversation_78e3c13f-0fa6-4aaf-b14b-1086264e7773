"use client"
import { useState } from "react"
import {
  HomeIcon,
  TruckIcon,
  UserGroupIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
  BellIcon,
  PowerIcon
} from "@heroicons/react/24/outline"

export default function Sidebar() {
  const [activeItem, setActiveItem] = useState("Dashboard")

  const menuItems = [
    { name: "Dashboard", icon: HomeIcon, active: true },
    { name: "Campagnes", icon: DocumentTextIcon },
    { name: "Chauffeur<PERSON>", icon: UserGroupIcon },
    { name: "Véhicules", icon: TruckIcon },
    { name: "Tarification", icon: ChartBarIcon },
    { name: "Paiements & Finances", icon: CogIcon },
    { name: "Litiges & Réclamations", icon: DocumentTextIcon },
    { name: "Notifications & Communication", icon: BellIcon },
  ]

  return (
    <div className="w-64 bg-white h-screen flex flex-col border-r border-gray-200">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">RP</span>
          </div>
          <h1 className="text-gray-900 font-semibold text-lg">Régie Pub</h1>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon
            const isActive = activeItem === item.name

            return (
              <li key={item.name}>
                <button
                  onClick={() => setActiveItem(item.name)}
                  className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    isActive
                      ? "bg-gray-800 text-white"
                      : "text-gray-600 hover:bg-black hover:text-white"
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </button>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* Logout Button */}
      <div className="p-4 border-t border-gray-200">
        <button
          onClick={() => console.log('Logout clicked')}
          className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-600 hover:bg-black hover:text-white transition-colors duration-200"
        >
          <ArrowRightOnRectangleIcon className="w-5 h-5" />
          <span>Logout</span>
        </button>
      </div>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <span className="text-gray-700 text-sm font-medium">MB</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-gray-900 text-sm font-medium truncate">M. Ben Abdellah</p>
            <p className="text-gray-500 text-xs truncate">Administrateur</p>
          </div>
        </div>
      </div>
    </div>
  )
}
