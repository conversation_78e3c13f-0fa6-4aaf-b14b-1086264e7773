"use client"
import { useState } from "react"
// import { Menu, X } from "lucide-react" // icons

export default function Sidebar() {
  const [open, setOpen] = useState(true)

  return (
    <div className="flex">
      {/* Sidebar */}
      <div
        className={`${
          open ? "w-64" : "w-16"
        } bg-gray-900 h-screen p-5 pt-8 relative duration-300`}
      >
        {/* Toggle Button */}
        <button
          onClick={() => setOpen(!open)}
          className="absolute -right-3 top-9 w-7 h-7 bg-white rounded-full flex items-center justify-center"
        >
          {/* {open ? <X size={18} /> : <Menu size={18} />} */}
        </button>

        {/* Logo */}
        <h1
          className={`text-white font-bold text-xl duration-200 ${
            !open && "scale-0"
          }`}
        >
          MyApp
        </h1>

        {/* Navigation Links */}
        <ul className="pt-6">
          <li className="text-gray-300 text-sm flex items-center gap-x-4 cursor-pointer p-2 hover:bg-gray-700 rounded-md">
            Dashboard
          </li>
          <li className="text-gray-300 text-sm flex items-center gap-x-4 cursor-pointer p-2 hover:bg-gray-700 rounded-md">
            Settings
          </li>
          <li className="text-gray-300 text-sm flex items-center gap-x-4 cursor-pointer p-2 hover:bg-gray-700 rounded-md">
            Profile
          </li>
        </ul>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-7">
        <h1 className="text-2xl font-semibold">Dashboard Content</h1>
      </div>
    </div>
  )
}
