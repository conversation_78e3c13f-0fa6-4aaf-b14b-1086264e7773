{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/TopBar.jsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TopBar.jsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TopBar.jsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/TopBar.jsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TopBar.jsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TopBar.jsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/app/chauffeurs/page.js"], "sourcesContent": ["import TopBar from \"@/components/TopBar\"\nimport { ChevronRightIcon } from \"@heroicons/react/24/outline\"\n\nexport default function Chauffeurs() {\n  // Sample driver data organized by campaigns\n  const campaigns = [\n    {\n      id: 1,\n      name: \"Campagne 1\",\n      drivers: [\n        { id: 1, name: \"<PERSON><PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 2, name: \"<PERSON><PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 3, name: \"<PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 4, name: \"<PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 5, name: \"<PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" }\n      ]\n    },\n    {\n      id: 2,\n      name: \"Campagne 1\",\n      drivers: [\n        { id: 6, name: \"<PERSON><PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 7, name: \"<PERSON><PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 8, name: \"<PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 9, name: \"<PERSON> <PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 10, name: \"<PERSON> <PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" }\n      ]\n    },\n    {\n      id: 3,\n      name: \"Campagne 1\",\n      drivers: [\n        { id: 11, name: \"<PERSON> <PERSON>\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 12, name: \"Ronald Richards\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 13, name: \"Savannah Nguyen\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 14, name: \"Eleanor Pena\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 15, name: \"Esther Howard\", avatar: \"/api/placeholder/32/32\", status: \"active\" }\n      ]\n    },\n    {\n      id: 4,\n      name: \"Campagne 1\",\n      drivers: [\n        { id: 16, name: \"Wade Warren\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 17, name: \"Brooklyn Simmons\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 18, name: \"Kristin Watson\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 19, name: \"Jacob Jones\", avatar: \"/api/placeholder/32/32\", status: \"active\" },\n        { id: 20, name: \"Cody Fisher\", avatar: \"/api/placeholder/32/32\", status: \"active\" }\n      ]\n    }\n  ]\n\n  const getInitials = (name) => {\n    return name.split(' ').map(n => n[0]).join('').toUpperCase()\n  }\n\n  const getAvatarColor = (name) => {\n    const colors = [\n      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', \n      'bg-pink-500', 'bg-indigo-500', 'bg-red-500', 'bg-orange-500'\n    ]\n    const index = name.length % colors.length\n    return colors[index]\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Bar */}\n      <TopBar />\n      \n      {/* Main Content */}\n      <div className=\"p-6\">\n        {/* Header */}\n        <div className=\"mb-6\">\n          <h1 className=\"text-2xl font-semibold text-gray-900 mb-2\">Compagnes</h1>\n          <p className=\"text-sm text-gray-600\">\n            Configuration des tarifs par zones, formats de véhicules et crénaux horaires\n          </p>\n        </div>\n\n        {/* Campaigns Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {campaigns.map((campaign) => (\n            <div key={campaign.id} className=\"bg-white rounded-lg border border-gray-200 shadow-sm\">\n              {/* Campaign Header */}\n              <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n                <h3 className=\"text-sm font-medium text-gray-900\">{campaign.name}</h3>\n                <button className=\"text-blue-600 hover:text-blue-700 text-xs font-medium\">\n                  View All\n                </button>\n              </div>\n\n              {/* Drivers List */}\n              <div className=\"p-4\">\n                <div className=\"space-y-3\">\n                  {campaign.drivers.map((driver) => (\n                    <div \n                      key={driver.id} \n                      className=\"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer group\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-8 h-8 ${getAvatarColor(driver.name)} rounded-full flex items-center justify-center`}>\n                          <span className=\"text-white text-xs font-medium\">\n                            {getInitials(driver.name)}\n                          </span>\n                        </div>\n                        <span className=\"text-sm font-medium text-gray-900\">\n                          {driver.name}\n                        </span>\n                      </div>\n                      \n                      <ChevronRightIcon className=\"w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors\" />\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,4CAA4C;IAC5C,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBACP;oBAAE,IAAI;oBAAG,MAAM;oBAAiB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACnF;oBAAE,IAAI;oBAAG,MAAM;oBAAgB,QAAQ;oBAA0B,QAAQ;gBAAS;gBAClF;oBAAE,IAAI;oBAAG,MAAM;oBAAe,QAAQ;oBAA0B,QAAQ;gBAAS;gBACjF;oBAAE,IAAI;oBAAG,MAAM;oBAAgB,QAAQ;oBAA0B,QAAQ;gBAAS;gBAClF;oBAAE,IAAI;oBAAG,MAAM;oBAAmB,QAAQ;oBAA0B,QAAQ;gBAAS;aACtF;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBACP;oBAAE,IAAI;oBAAG,MAAM;oBAAkB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACpF;oBAAE,IAAI;oBAAG,MAAM;oBAAkB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACpF;oBAAE,IAAI;oBAAG,MAAM;oBAAkB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACpF;oBAAE,IAAI;oBAAG,MAAM;oBAAkB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACpF;oBAAE,IAAI;oBAAI,MAAM;oBAAiB,QAAQ;oBAA0B,QAAQ;gBAAS;aACrF;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBACP;oBAAE,IAAI;oBAAI,MAAM;oBAAoB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACvF;oBAAE,IAAI;oBAAI,MAAM;oBAAmB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACtF;oBAAE,IAAI;oBAAI,MAAM;oBAAmB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACtF;oBAAE,IAAI;oBAAI,MAAM;oBAAgB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACnF;oBAAE,IAAI;oBAAI,MAAM;oBAAiB,QAAQ;oBAA0B,QAAQ;gBAAS;aACrF;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,SAAS;gBACP;oBAAE,IAAI;oBAAI,MAAM;oBAAe,QAAQ;oBAA0B,QAAQ;gBAAS;gBAClF;oBAAE,IAAI;oBAAI,MAAM;oBAAoB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACvF;oBAAE,IAAI;oBAAI,MAAM;oBAAkB,QAAQ;oBAA0B,QAAQ;gBAAS;gBACrF;oBAAE,IAAI;oBAAI,MAAM;oBAAe,QAAQ;oBAA0B,QAAQ;gBAAS;gBAClF;oBAAE,IAAI;oBAAI,MAAM;oBAAe,QAAQ;oBAA0B,QAAQ;gBAAS;aACnF;QACH;KACD;IAED,MAAM,cAAc,CAAC;QACnB,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,WAAW;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb;YAAe;YAAgB;YAAiB;YAChD;YAAe;YAAiB;YAAc;SAC/C;QACD,MAAM,QAAQ,KAAK,MAAM,GAAG,OAAO,MAAM;QACzC,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uIAAM;;;;;0BAGP,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAC1D,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;gCAAsB,WAAU;;kDAE/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAqC,SAAS,IAAI;;;;;;0DAChE,8OAAC;gDAAO,WAAU;0DAAwD;;;;;;;;;;;;kDAM5E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,uBACrB,8OAAC;oDAEC,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAW,CAAC,QAAQ,EAAE,eAAe,OAAO,IAAI,EAAE,8CAA8C,CAAC;8EACpG,cAAA,8OAAC;wEAAK,WAAU;kFACb,YAAY,OAAO,IAAI;;;;;;;;;;;8EAG5B,8OAAC;oEAAK,WAAU;8EACb,OAAO,IAAI;;;;;;;;;;;;sEAIhB,8OAAC,mPAAgB;4DAAC,WAAU;;;;;;;mDAdvB,OAAO,EAAE;;;;;;;;;;;;;;;;+BAdd,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAuCjC", "debugId": null}}]}