"use client"

export default function AvailabilityTable() {
  const availabilityData = [
    {
      zone: "Zone 1",
      availability: 85,
      status: "Disponible",
      vehicles: 24,
      color: "bg-green-500"
    },
    {
      zone: "Zone 2", 
      availability: 68,
      status: "Disponible",
      vehicles: 18,
      color: "bg-yellow-500"
    },
    {
      zone: "Zone 3",
      availability: 92,
      status: "Disponible", 
      vehicles: 31,
      color: "bg-green-500"
    },
    {
      zone: "Zone 4",
      availability: 45,
      status: "Critique",
      vehicles: 12,
      color: "bg-red-500"
    },
    {
      zone: "Zone 5",
      availability: 78,
      status: "Disponible",
      vehicles: 22,
      color: "bg-green-500"
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Disponible":
        return "text-green-600 bg-green-100"
      case "Critique":
        return "text-red-600 bg-red-100"
      default:
        return "text-gray-600 bg-gray-100"
    }
  }

  return (
    <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Flotte disponible par zone</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Dernière mise à jour:</span>
          <span className="text-sm font-medium text-gray-900">Il y a 5 min</span>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Zone</th>
              <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Disponibilité</th>
              <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Statut</th>
              <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Véhicules</th>
              <th className="text-left py-3 px-4 font-medium text-gray-600 text-sm">Progression</th>
            </tr>
          </thead>
          <tbody>
            {availabilityData.map((item, index) => (
              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
                    <span className="font-medium text-gray-900">{item.zone}</span>
                  </div>
                </td>
                <td className="py-4 px-4">
                  <span className="text-gray-900 font-medium">{item.availability}%</span>
                </td>
                <td className="py-4 px-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <span className="text-gray-900">{item.vehicles}</span>
                </td>
                <td className="py-4 px-4">
                  <div className="flex items-center space-x-3">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${item.color} transition-all duration-300`}
                        style={{ width: `${item.availability}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 min-w-[3rem]">{item.availability}%</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-100 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Affichage de 5 zones sur 12 au total
        </div>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          Voir toutes les zones
        </button>
      </div>
    </div>
  )
}
