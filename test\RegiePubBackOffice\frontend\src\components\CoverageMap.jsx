"use client"

export default function CoverageMap() {
  return (
    <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm h-full">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Couverture</h3>
      </div>
      
      {/* Simplified map representation */}
      <div className="relative h-64 bg-gray-50 rounded-lg overflow-hidden">
        {/* Map background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100">
          {/* Coverage areas - represented as colored regions */}
          <div className="absolute top-8 left-12 w-16 h-12 bg-blue-600 rounded-lg opacity-80"></div>
          <div className="absolute top-16 right-16 w-20 h-16 bg-blue-500 rounded-lg opacity-70"></div>
          <div className="absolute bottom-12 left-8 w-14 h-10 bg-blue-700 rounded-lg opacity-90"></div>
          <div className="absolute bottom-8 right-12 w-18 h-14 bg-blue-400 rounded-lg opacity-60"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-8 bg-blue-800 rounded-lg opacity-85"></div>
          
          {/* Additional smaller coverage points */}
          <div className="absolute top-6 right-8 w-6 h-6 bg-blue-600 rounded-full opacity-75"></div>
          <div className="absolute bottom-16 left-16 w-8 h-8 bg-blue-500 rounded-full opacity-80"></div>
          <div className="absolute top-20 left-1/3 w-4 h-4 bg-blue-700 rounded-full opacity-90"></div>
        </div>
        
        {/* Map overlay with grid lines */}
        <div className="absolute inset-0 opacity-20">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#374151" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
        
        {/* Legend */}
        <div className="absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-sm">
          <div className="flex items-center space-x-4 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-blue-600 rounded"></div>
              <span className="text-gray-600">Zone couverte</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-gray-300 rounded"></div>
              <span className="text-gray-600">Zone non couverte</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* Coverage stats */}
      <div className="mt-4 grid grid-cols-2 gap-4">
        <div className="text-center">
          <p className="text-2xl font-bold text-blue-600">85%</p>
          <p className="text-sm text-gray-500">Couverture totale</p>
        </div>
        <div className="text-center">
          <p className="text-2xl font-bold text-green-600">12</p>
          <p className="text-sm text-gray-500">Zones actives</p>
        </div>
      </div>
    </div>
  )
}
