"use client"

export default function CoverageMap() {
  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-900">Couverture</h3>
      </div>

      {/* Simplified map representation matching the image */}
      <div className="relative h-32 bg-gray-50 rounded-lg overflow-hidden mb-3">
        {/* Map background with country/region shapes */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100">
          {/* Main coverage areas - simplified country/region shapes */}
          <div className="absolute top-4 left-6 w-8 h-6 bg-blue-600 rounded-sm opacity-80 transform rotate-12"></div>
          <div className="absolute top-6 right-8 w-10 h-8 bg-blue-500 rounded-sm opacity-70 transform -rotate-6"></div>
          <div className="absolute bottom-6 left-4 w-6 h-4 bg-blue-700 rounded-sm opacity-90 transform rotate-45"></div>
          <div className="absolute bottom-4 right-6 w-8 h-6 bg-blue-400 rounded-sm opacity-60 transform -rotate-12"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-4 bg-blue-800 rounded-sm opacity-85"></div>

          {/* Additional smaller coverage areas */}
          <div className="absolute top-3 right-4 w-3 h-3 bg-blue-600 rounded-full opacity-75"></div>
          <div className="absolute bottom-8 left-8 w-4 h-4 bg-blue-500 rounded-full opacity-80"></div>
          <div className="absolute top-10 left-1/3 w-2 h-2 bg-blue-700 rounded-full opacity-90"></div>
        </div>
      </div>
    </div>
  )
}
