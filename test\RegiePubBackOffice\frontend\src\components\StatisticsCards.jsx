"use client"
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/solid"

export default function StatisticsCards() {
  const statsData = [
    {
      title: "Nombre de véhicules",
      value: "40,689",
      change: "+4.5%",
      trend: "up",
      subtitle: "vs mois précédent",
      icon: "🚗",
      color: "text-blue-600"
    },
    {
      title: "Revenus générés",
      value: "589,000",
      change: "+8.2%",
      trend: "up", 
      subtitle: "€ vs mois précédent",
      icon: "💰",
      color: "text-green-600"
    },
    {
      title: "Utilisateurs actifs",
      value: "2040",
      change: "-2.1%",
      trend: "down",
      subtitle: "vs la semaine dernière",
      icon: "👥",
      color: "text-purple-600"
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {statsData.map((stat, index) => (
        <div key={index} className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
          <div className="flex items-center justify-between mb-4">
            <div className="text-2xl">{stat.icon}</div>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${
              stat.trend === 'up' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {stat.trend === 'up' ? (
                <ArrowUpIcon className="w-3 h-3" />
              ) : (
                <ArrowDownIcon className="w-3 h-3" />
              )}
              <span>{stat.change}</span>
            </div>
          </div>
          
          <div className="mb-2">
            <h3 className="text-sm font-medium text-gray-600 mb-1">
              {stat.title}
            </h3>
            <p className={`text-2xl font-bold ${stat.color}`}>
              {stat.value}
            </p>
          </div>
          
          <p className="text-xs text-gray-500">
            {stat.subtitle}
          </p>
        </div>
      ))}
    </div>
  )
}
