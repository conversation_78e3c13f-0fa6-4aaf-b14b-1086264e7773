"use client"
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/solid"

export default function StatisticsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {/* Nombre de campagnes en cours */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
            </svg>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Nombre de campagnes en cours</p>
            <p className="text-lg font-bold text-gray-900">40,689</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowUpIcon className="w-3 h-3 text-green-500 mr-1" />
          <span className="text-green-500">8.5%</span>
          <span className="ml-1">Up from yesterday</span>
        </div>
      </div>

      {/* Revenus générés */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Revenus générés</p>
            <p className="text-lg font-bold text-gray-900">$89,000</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowDownIcon className="w-3 h-3 text-red-500 mr-1" />
          <span className="text-red-500">4.3%</span>
          <span className="ml-1">Down from yesterday</span>
        </div>
      </div>

      {/* Litiges ouverts / incidents signalés */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
            <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Litiges ouverts / incidents signalés</p>
            <p className="text-lg font-bold text-gray-900">2040</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowUpIcon className="w-3 h-3 text-green-500 mr-1" />
          <span className="text-green-500">1.8%</span>
          <span className="ml-1">Up from yesterday</span>
        </div>
      </div>
    </div>
  )
}
