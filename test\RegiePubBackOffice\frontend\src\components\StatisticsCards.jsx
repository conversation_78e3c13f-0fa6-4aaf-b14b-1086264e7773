"use client"
import { ArrowUpIcon, ArrowDownIcon } from "@heroicons/react/24/solid"

export default function StatisticsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {/* Nombre de véhicules */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <span className="text-blue-600 text-sm">📊</span>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Nombre de véhicules</p>
            <p className="text-lg font-bold text-gray-900">40,689</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowUpIcon className="w-3 h-3 text-green-500 mr-1" />
          <span>+4.5% vs mois précédent</span>
        </div>
      </div>

      {/* Revenus générés */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <span className="text-green-600 text-sm">💰</span>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Revenus générés</p>
            <p className="text-lg font-bold text-gray-900">589,000</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowUpIcon className="w-3 h-3 text-green-500 mr-1" />
          <span>+8.2% vs mois précédent</span>
        </div>
      </div>

      {/* Utilisateurs actifs */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
            <span className="text-orange-600 text-sm">👥</span>
          </div>
          <div className="flex-1">
            <p className="text-xs text-gray-500 mb-1">Utilisateurs actifs</p>
            <p className="text-lg font-bold text-gray-900">2040</p>
          </div>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <ArrowUpIcon className="w-3 h-3 text-green-500 mr-1" />
          <span>+2.1% vs la semaine dernière</span>
        </div>
      </div>
    </div>
  )
}
