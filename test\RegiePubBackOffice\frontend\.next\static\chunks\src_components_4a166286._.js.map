{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/TopBar.jsx"], "sourcesContent": ["\"use client\"\nimport { \n  MagnifyingGlassIcon, \n  BellIcon, \n  UserCircleIcon,\n  ChevronDownIcon\n} from \"@heroicons/react/24/outline\"\n\nexport default function TopBar() {\n  return (\n    <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-lg\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher...\"\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Right Side - Language, Notifications, Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Language Selector */}\n          <div className=\"flex items-center space-x-1\">\n            <div className=\"w-6 h-4 bg-blue-600 rounded-sm flex items-center justify-center\">\n              <span className=\"text-white text-xs font-bold\">FR</span>\n            </div>\n            <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\">\n              <BellIcon className=\"h-6 w-6\" />\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n              <div className=\"hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-700\">M. Ben Abdellah</p>\n                <p className=\"text-xs text-gray-500\">Administrateur</p>\n              </div>\n              <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AADA;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+PAAmB;oCAAC,WAAU;;;;;;;;;;;0CAEjC,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC,mPAAe;oCAAC,WAAU;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,8NAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gPAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC,mPAAe;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;KAnDwB", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/KPICards.jsx"], "sourcesContent": ["\"use client\"\nimport { TruckIcon, UserPlusIcon, MapIcon } from \"@heroicons/react/24/outline\"\n\nexport default function KPICards() {\n  const kpiData = [\n    {\n      title: \"Gérer les véhicules\",\n      icon: TruckIcon,\n      bgColor: \"bg-blue-50\",\n      iconColor: \"text-blue-600\",\n      buttonText: \"Gérer les véhicules\",\n      buttonColor: \"bg-blue-600 hover:bg-blue-700\"\n    },\n    {\n      title: \"Ajouter un chauffeur\",\n      icon: UserPlusIcon,\n      bgColor: \"bg-green-50\",\n      iconColor: \"text-green-600\",\n      buttonText: \"Ajouter un chauffeur\",\n      buttonColor: \"bg-green-600 hover:bg-green-700\"\n    },\n    {\n      title: \"Couverture\",\n      icon: MapIcon,\n      bgColor: \"bg-purple-50\",\n      iconColor: \"text-purple-600\",\n      buttonText: \"Voir la couverture\",\n      buttonColor: \"bg-purple-600 hover:bg-purple-700\"\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n      {kpiData.map((kpi, index) => {\n        const Icon = kpi.icon\n        return (\n          <div key={index} className={`${kpi.bgColor} rounded-xl p-6 border border-gray-100`}>\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className={`p-3 rounded-lg ${kpi.bgColor}`}>\n                <Icon className={`w-8 h-8 ${kpi.iconColor}`} />\n              </div>\n            </div>\n            \n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n              {kpi.title}\n            </h3>\n            \n            <button className={`w-full ${kpi.buttonColor} text-white py-2.5 px-4 rounded-lg font-medium transition-colors duration-200`}>\n              {kpi.buttonText}\n            </button>\n          </div>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AADA;;;AAGe,SAAS;IACtB,MAAM,UAAU;QACd;YACE,OAAO;YACP,MAAM,iOAAS;YACf,SAAS;YACT,WAAW;YACX,YAAY;YACZ,aAAa;QACf;QACA;YACE,OAAO;YACP,MAAM,0OAAY;YAClB,SAAS;YACT,WAAW;YACX,YAAY;YACZ,aAAa;QACf;QACA;YACE,OAAO;YACP,MAAM,2NAAO;YACb,SAAS;YACT,WAAW;YACX,YAAY;YACZ,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,QAAQ,GAAG,CAAC,CAAC,KAAK;YACjB,MAAM,OAAO,IAAI,IAAI;YACrB,qBACE,6LAAC;gBAAgB,WAAW,AAAC,GAAc,OAAZ,IAAI,OAAO,EAAC;;kCACzC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,AAAC,kBAA6B,OAAZ,IAAI,OAAO;sCAC3C,cAAA,6LAAC;gCAAK,WAAW,AAAC,WAAwB,OAAd,IAAI,SAAS;;;;;;;;;;;;;;;;kCAI7C,6LAAC;wBAAG,WAAU;kCACX,IAAI,KAAK;;;;;;kCAGZ,6LAAC;wBAAO,WAAW,AAAC,UAAyB,OAAhB,IAAI,WAAW,EAAC;kCAC1C,IAAI,UAAU;;;;;;;eAZT;;;;;QAgBd;;;;;;AAGN;KApDwB", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/StatisticsCards.jsx"], "sourcesContent": ["\"use client\"\nimport { ArrowUpIcon, ArrowDownIcon } from \"@heroicons/react/24/solid\"\n\nexport default function StatisticsCards() {\n  const statsData = [\n    {\n      title: \"Nombre de véhicules\",\n      value: \"40,689\",\n      change: \"+4.5%\",\n      trend: \"up\",\n      subtitle: \"vs mois précédent\",\n      icon: \"🚗\",\n      color: \"text-blue-600\"\n    },\n    {\n      title: \"Revenus générés\",\n      value: \"589,000\",\n      change: \"+8.2%\",\n      trend: \"up\", \n      subtitle: \"€ vs mois précédent\",\n      icon: \"💰\",\n      color: \"text-green-600\"\n    },\n    {\n      title: \"Utilisateurs actifs\",\n      value: \"2040\",\n      change: \"-2.1%\",\n      trend: \"down\",\n      subtitle: \"vs la semaine dernière\",\n      icon: \"👥\",\n      color: \"text-purple-600\"\n    }\n  ]\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\">\n      {statsData.map((stat, index) => (\n        <div key={index} className=\"bg-white rounded-xl p-6 border border-gray-100 shadow-sm\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"text-2xl\">{stat.icon}</div>\n            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${\n              stat.trend === 'up' \n                ? 'bg-green-100 text-green-800' \n                : 'bg-red-100 text-red-800'\n            }`}>\n              {stat.trend === 'up' ? (\n                <ArrowUpIcon className=\"w-3 h-3\" />\n              ) : (\n                <ArrowDownIcon className=\"w-3 h-3\" />\n              )}\n              <span>{stat.change}</span>\n            </div>\n          </div>\n          \n          <div className=\"mb-2\">\n            <h3 className=\"text-sm font-medium text-gray-600 mb-1\">\n              {stat.title}\n            </h3>\n            <p className={`text-2xl font-bold ${stat.color}`}>\n              {stat.value}\n            </p>\n          </div>\n          \n          <p className=\"text-xs text-gray-500\">\n            {stat.subtitle}\n          </p>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AADA;;;AAGe,SAAS;IACtB,MAAM,YAAY;QAChB;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,MAAM;YACN,OAAO;QACT;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;gBAAgB,WAAU;;kCACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAA<PERSON>,WAAU;0CAAY,KAAK,IAAI;;;;;;0CACpC,6LAAC;gCAAI,WAAW,AAAC,0EAIhB,OAHC,KAAK,KAAK,KAAK,OACX,gCACA;;oCAEH,KAAK,KAAK,KAAK,qBACd,6LAAC,qOAAW;wCAAC,WAAU;;;;;6DAEvB,6LAAC,2OAAa;wCAAC,WAAU;;;;;;kDAE3B,6LAAC;kDAAM,KAAK,MAAM;;;;;;;;;;;;;;;;;;kCAItB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;0CAEb,6LAAC;gCAAE,WAAW,AAAC,sBAAgC,OAAX,KAAK,KAAK;0CAC3C,KAAK,KAAK;;;;;;;;;;;;kCAIf,6LAAC;wBAAE,WAAU;kCACV,KAAK,QAAQ;;;;;;;eA3BR;;;;;;;;;;AAiClB;KAnEwB", "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/ConversionChart.jsx"], "sourcesContent": ["\"use client\"\nimport { Line } from 'react-chartjs-2'\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n} from 'chart.js'\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n)\n\nexport default function ConversionChart() {\n  const data = {\n    labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    datasets: [\n      {\n        label: 'Site Conversion',\n        data: [65, 78, 85, 72, 88, 95, 82],\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 3,\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: 'rgb(59, 130, 246)',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 6,\n        pointHoverRadius: 8,\n      }\n    ]\n  }\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: false,\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: 'rgba(59, 130, 246, 0.5)',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: false,\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false,\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          }\n        }\n      },\n      y: {\n        grid: {\n          color: 'rgba(107, 114, 128, 0.1)',\n          borderDash: [5, 5],\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          },\n          callback: function(value) {\n            return value + '%'\n          }\n        },\n        min: 0,\n        max: 100,\n      }\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index',\n    },\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-100 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Nombre de conversion</h3>\n          <p className=\"text-sm text-gray-500\">Conversion rate by day, last week</p>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n            <span className=\"text-sm text-gray-600\">Site Conversion</span>\n          </div>\n        </div>\n      </div>\n      \n      <div className=\"h-80\">\n        <Line data={data} options={options} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAcA,wKAAO,CAAC,QAAQ,CACd,gLAAa,EACb,8KAAW,EACX,+KAAY,EACZ,8KAAW,EACX,wKAAK,EACL,0KAAO,EACP,yKAAM,EACN,yKAAM;AAGO,SAAS;IACtB,MAAM,OAAO;QACX,QAAQ;YAAC;YAAU;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QACtF,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG;gBAClC,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,sBAAsB;gBACtB,kBAAkB;gBAClB,kBAAkB;gBAClB,aAAa;gBACb,kBAAkB;YACpB;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;YACX;YACA,SAAS;gBACP,iBAAiB;gBACjB,YAAY;gBACZ,WAAW;gBACX,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,eAAe;YACjB;QACF;QACA,QAAQ;YACN,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,OAAO;oBACP,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;oBACA,UAAU,SAAS,KAAK;wBACtB,OAAO,QAAQ;oBACjB;gBACF;gBACA,KAAK;gBACL,KAAK;YACP;QACF;QACA,aAAa;YACX,WAAW;YACX,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iKAAI;oBAAC,MAAM;oBAAM,SAAS;;;;;;;;;;;;;;;;;AAInC;KAvGwB", "debugId": null}}, {"offset": {"line": 670, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/CoverageMap.jsx"], "sourcesContent": ["\"use client\"\n\nexport default function CoverageMap() {\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-100 shadow-sm h-full\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Couverture</h3>\n      </div>\n      \n      {/* Simplified map representation */}\n      <div className=\"relative h-64 bg-gray-50 rounded-lg overflow-hidden\">\n        {/* Map background */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100\">\n          {/* Coverage areas - represented as colored regions */}\n          <div className=\"absolute top-8 left-12 w-16 h-12 bg-blue-600 rounded-lg opacity-80\"></div>\n          <div className=\"absolute top-16 right-16 w-20 h-16 bg-blue-500 rounded-lg opacity-70\"></div>\n          <div className=\"absolute bottom-12 left-8 w-14 h-10 bg-blue-700 rounded-lg opacity-90\"></div>\n          <div className=\"absolute bottom-8 right-12 w-18 h-14 bg-blue-400 rounded-lg opacity-60\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-8 bg-blue-800 rounded-lg opacity-85\"></div>\n          \n          {/* Additional smaller coverage points */}\n          <div className=\"absolute top-6 right-8 w-6 h-6 bg-blue-600 rounded-full opacity-75\"></div>\n          <div className=\"absolute bottom-16 left-16 w-8 h-8 bg-blue-500 rounded-full opacity-80\"></div>\n          <div className=\"absolute top-20 left-1/3 w-4 h-4 bg-blue-700 rounded-full opacity-90\"></div>\n        </div>\n        \n        {/* Map overlay with grid lines */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\">\n            <defs>\n              <pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\">\n                <path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"#374151\" strokeWidth=\"0.5\"/>\n              </pattern>\n            </defs>\n            <rect width=\"100\" height=\"100\" fill=\"url(#grid)\" />\n          </svg>\n        </div>\n        \n        {/* Legend */}\n        <div className=\"absolute bottom-4 left-4 bg-white rounded-lg p-3 shadow-sm\">\n          <div className=\"flex items-center space-x-4 text-xs\">\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-3 h-3 bg-blue-600 rounded\"></div>\n              <span className=\"text-gray-600\">Zone couverte</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <div className=\"w-3 h-3 bg-gray-300 rounded\"></div>\n              <span className=\"text-gray-600\">Zone non couverte</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Coverage stats */}\n      <div className=\"mt-4 grid grid-cols-2 gap-4\">\n        <div className=\"text-center\">\n          <p className=\"text-2xl font-bold text-blue-600\">85%</p>\n          <p className=\"text-sm text-gray-500\">Couverture totale</p>\n        </div>\n        <div className=\"text-center\">\n          <p className=\"text-2xl font-bold text-green-600\">12</p>\n          <p className=\"text-sm text-gray-500\">Zones actives</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAsC;;;;;;;;;;;0BAItD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAgB,SAAQ;4BAAc,qBAAoB;;8CACvE,6LAAC;8CACC,cAAA,6LAAC;wCAAQ,IAAG;wCAAO,OAAM;wCAAK,QAAO;wCAAK,cAAa;kDACrD,cAAA,6LAAC;4CAAK,GAAE;4CAAoB,MAAK;4CAAO,QAAO;4CAAU,aAAY;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAK,OAAM;oCAAM,QAAO;oCAAM,MAAK;;;;;;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;8CAElC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;AAK/C;KAhEwB", "debugId": null}}, {"offset": {"line": 963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/CriticalAlerts.jsx"], "sourcesContent": ["\"use client\"\nimport { ExclamationTriangleIcon, InformationCircleIcon } from \"@heroicons/react/24/outline\"\n\nexport default function CriticalAlerts() {\n  const alerts = [\n    {\n      id: 1,\n      type: \"warning\",\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone Sud • 68% dispo\",\n      time: \"Il y a 2h\",\n      icon: ExclamationTriangleIcon,\n      bgColor: \"bg-orange-50\",\n      iconColor: \"text-orange-600\",\n      borderColor: \"border-orange-200\"\n    },\n    {\n      id: 2,\n      type: \"error\",\n      title: \"Chauffeurs non validés\",\n      description: \"5 chauffeurs en attente\",\n      time: \"Il y a 3h\",\n      icon: ExclamationTriangleIcon,\n      bgColor: \"bg-red-50\",\n      iconColor: \"text-red-600\",\n      borderColor: \"border-red-200\"\n    },\n    {\n      id: 3,\n      type: \"warning\",\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone ouest • 52% dispo\",\n      time: \"Il y a 4h\",\n      icon: ExclamationTriangleIcon,\n      bgColor: \"bg-orange-50\",\n      iconColor: \"text-orange-600\",\n      borderColor: \"border-orange-200\"\n    },\n    {\n      id: 4,\n      type: \"info\",\n      title: \"Urgent non traités\",\n      description: \"12 incidents ouverts\",\n      time: \"Il y a 5h\",\n      icon: InformationCircleIcon,\n      bgColor: \"bg-blue-50\",\n      iconColor: \"text-blue-600\",\n      borderColor: \"border-blue-200\"\n    }\n  ]\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-100 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Alertes critiques</h3>\n        <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n          Sync Recent\n        </button>\n      </div>\n      \n      <div className=\"space-y-4\">\n        {alerts.map((alert) => {\n          const Icon = alert.icon\n          return (\n            <div \n              key={alert.id} \n              className={`${alert.bgColor} ${alert.borderColor} border rounded-lg p-4 transition-all duration-200 hover:shadow-sm`}\n            >\n              <div className=\"flex items-start space-x-3\">\n                <div className={`p-2 rounded-lg ${alert.bgColor}`}>\n                  <Icon className={`w-5 h-5 ${alert.iconColor}`} />\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-sm font-medium text-gray-900 mb-1\">\n                    {alert.title}\n                  </h4>\n                  <p className=\"text-sm text-gray-600 mb-2\">\n                    {alert.description}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {alert.time}\n                  </p>\n                </div>\n                \n                <button className=\"text-gray-400 hover:text-gray-600\">\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-100\">\n        <button className=\"w-full text-center text-blue-600 hover:text-blue-700 text-sm font-medium py-2\">\n          Voir toutes les alertes\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AADA;;;AAGe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,2QAAuB;YAC7B,SAAS;YACT,WAAW;YACX,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,2QAAuB;YAC7B,SAAS;YACT,WAAW;YACX,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM,2QAAuB;YAC7B,SAAS;YACT,WAAW;YACX,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;YACb,MAAM;YAC<PERSON>,MAAM,qQAAqB;YAC3B,SAAS;YACT,WAAW;YACX,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAO,WAAU;kCAAwD;;;;;;;;;;;;0BAK5E,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC;oBACX,MAAM,OAAO,MAAM,IAAI;oBACvB,qBACE,6LAAC;wBAEC,WAAW,AAAC,GAAmB,OAAjB,MAAM,OAAO,EAAC,KAAqB,OAAlB,MAAM,WAAW,EAAC;kCAEjD,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAW,AAAC,kBAA+B,OAAd,MAAM,OAAO;8CAC7C,cAAA,6LAAC;wCAAK,WAAW,AAAC,WAA0B,OAAhB,MAAM,SAAS;;;;;;;;;;;8CAG7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAEpB,6LAAC;4CAAE,WAAU;sDACV,MAAM,IAAI;;;;;;;;;;;;8CAIf,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,6LAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;uBAtB1O,MAAM,EAAE;;;;;gBA4BnB;;;;;;0BAGF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAO,WAAU;8BAAgF;;;;;;;;;;;;;;;;;AAM1G;KApGwB", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/AvailabilityTable.jsx"], "sourcesContent": ["\"use client\"\n\nexport default function AvailabilityTable() {\n  const availabilityData = [\n    {\n      zone: \"Zone 1\",\n      availability: 85,\n      status: \"Disponible\",\n      vehicles: 24,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 2\", \n      availability: 68,\n      status: \"Disponible\",\n      vehicles: 18,\n      color: \"bg-yellow-500\"\n    },\n    {\n      zone: \"Zone 3\",\n      availability: 92,\n      status: \"Disponible\", \n      vehicles: 31,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 4\",\n      availability: 45,\n      status: \"Critique\",\n      vehicles: 12,\n      color: \"bg-red-500\"\n    },\n    {\n      zone: \"Zone 5\",\n      availability: 78,\n      status: \"Disponible\",\n      vehicles: 22,\n      color: \"bg-green-500\"\n    }\n  ]\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case \"Disponible\":\n        return \"text-green-600 bg-green-100\"\n      case \"Critique\":\n        return \"text-red-600 bg-red-100\"\n      default:\n        return \"text-gray-600 bg-gray-100\"\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-100 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">Flotte disponible par zone</h3>\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm text-gray-500\">Dernière mise à jour:</span>\n          <span className=\"text-sm font-medium text-gray-900\">Il y a 5 min</span>\n        </div>\n      </div>\n      \n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead>\n            <tr className=\"border-b border-gray-200\">\n              <th className=\"text-left py-3 px-4 font-medium text-gray-600 text-sm\">Zone</th>\n              <th className=\"text-left py-3 px-4 font-medium text-gray-600 text-sm\">Disponibilité</th>\n              <th className=\"text-left py-3 px-4 font-medium text-gray-600 text-sm\">Statut</th>\n              <th className=\"text-left py-3 px-4 font-medium text-gray-600 text-sm\">Véhicules</th>\n              <th className=\"text-left py-3 px-4 font-medium text-gray-600 text-sm\">Progression</th>\n            </tr>\n          </thead>\n          <tbody>\n            {availabilityData.map((item, index) => (\n              <tr key={index} className=\"border-b border-gray-100 hover:bg-gray-50 transition-colors\">\n                <td className=\"py-4 px-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className={`w-3 h-3 rounded-full ${item.color}`}></div>\n                    <span className=\"font-medium text-gray-900\">{item.zone}</span>\n                  </div>\n                </td>\n                <td className=\"py-4 px-4\">\n                  <span className=\"text-gray-900 font-medium\">{item.availability}%</span>\n                </td>\n                <td className=\"py-4 px-4\">\n                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>\n                    {item.status}\n                  </span>\n                </td>\n                <td className=\"py-4 px-4\">\n                  <span className=\"text-gray-900\">{item.vehicles}</span>\n                </td>\n                <td className=\"py-4 px-4\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full ${item.color} transition-all duration-300`}\n                        style={{ width: `${item.availability}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm text-gray-600 min-w-[3rem]\">{item.availability}%</span>\n                  </div>\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n      \n      <div className=\"mt-6 pt-4 border-t border-gray-100 flex justify-between items-center\">\n        <div className=\"text-sm text-gray-500\">\n          Affichage de 5 zones sur 12 au total\n        </div>\n        <button className=\"text-blue-600 hover:text-blue-700 text-sm font-medium\">\n          Voir toutes les zones\n        </button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAK,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIxD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDACtE,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;;;;;;;;;;;;sCAG1E,6LAAC;sCACE,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;oCAAe,WAAU;;sDACxB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAAkC,OAAX,KAAK,KAAK;;;;;;kEAClD,6LAAC;wDAAK,WAAU;kEAA6B,KAAK,IAAI;;;;;;;;;;;;;;;;;sDAG1D,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAU;;oDAA6B,KAAK,YAAY;oDAAC;;;;;;;;;;;;sDAEjE,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAW,AAAC,8CAAyE,OAA5B,eAAe,KAAK,MAAM;0DACtF,KAAK,MAAM;;;;;;;;;;;sDAGhB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAK,WAAU;0DAAiB,KAAK,QAAQ;;;;;;;;;;;sDAEhD,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,AAAC,oBAA8B,OAAX,KAAK,KAAK,EAAC;4DAC1C,OAAO;gEAAE,OAAO,AAAC,GAAoB,OAAlB,KAAK,YAAY,EAAC;4DAAG;;;;;;;;;;;kEAG5C,6LAAC;wDAAK,WAAU;;4DAAsC,KAAK,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;mCA1BrE;;;;;;;;;;;;;;;;;;;;;0BAmCjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;kCAGvC,6LAAC;wBAAO,WAAU;kCAAwD;;;;;;;;;;;;;;;;;;AAMlF;KAtHwB", "debugId": null}}]}