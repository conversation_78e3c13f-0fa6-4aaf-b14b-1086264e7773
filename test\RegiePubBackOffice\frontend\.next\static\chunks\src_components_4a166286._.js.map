{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/TopBar.jsx"], "sourcesContent": ["\"use client\"\nimport { \n  MagnifyingGlassIcon, \n  BellIcon, \n  UserCircleIcon,\n  ChevronDownIcon\n} from \"@heroicons/react/24/outline\"\n\nexport default function TopBar() {\n  return (\n    <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-lg\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher...\"\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Right Side - Language, Notifications, Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Language Selector */}\n          <div className=\"flex items-center space-x-1\">\n            <div className=\"w-6 h-4 bg-blue-600 rounded-sm flex items-center justify-center\">\n              <span className=\"text-white text-xs font-bold\">FR</span>\n            </div>\n            <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\">\n              <BellIcon className=\"h-6 w-6\" />\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n              <div className=\"hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-700\">M. Ben Abdellah</p>\n                <p className=\"text-xs text-gray-500\">Administrateur</p>\n              </div>\n              <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AADA;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+PAAmB;oCAAC,WAAU;;;;;;;;;;;0CAEjC,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC,mPAAe;oCAAC,WAAU;;;;;;;;;;;;sCAI7B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,8NAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;sCAKpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gPAAc;wCAAC,WAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC,mPAAe;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;KAnDwB", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/KPICards.jsx"], "sourcesContent": ["\"use client\"\nimport { TruckIcon, UserPlusIcon } from \"@heroicons/react/24/outline\"\n\nexport default function KPICards() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n      {/* Gérer les véhicules */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <TruckIcon className=\"w-6 h-6 text-blue-600\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Gérer les véhicules</h3>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-xs text-gray-500\">Nouveau lot</span>\n          <button className=\"bg-gray-800 text-white text-xs px-3 py-1.5 rounded-md hover:bg-gray-900 transition-colors\">\n            Gérer les véhicules\n          </button>\n        </div>\n      </div>\n\n      {/* Ajouter un chauffeur */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <UserPlusIcon className=\"w-6 h-6 text-blue-600\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Ajouter un chauffeur</h3>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-xs text-gray-500\">Nouveau lot</span>\n          <button className=\"bg-blue-600 text-white text-xs px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors\">\n            Ajouter un chauffeur\n          </button>\n        </div>\n      </div>\n\n      {/* Couverture */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h3 className=\"text-sm font-medium text-gray-900\">Couverture</h3>\n        </div>\n        <div className=\"relative h-16 bg-gray-50 rounded-md overflow-hidden\">\n          {/* Simple map representation */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200\">\n            <div className=\"absolute top-2 left-2 w-3 h-2 bg-blue-600 rounded-sm opacity-80\"></div>\n            <div className=\"absolute top-3 right-3 w-4 h-3 bg-blue-500 rounded-sm opacity-70\"></div>\n            <div className=\"absolute bottom-2 left-3 w-2 h-2 bg-blue-700 rounded-sm opacity-90\"></div>\n            <div className=\"absolute bottom-2 right-2 w-3 h-2 bg-blue-400 rounded-sm opacity-60\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AADA;;;AAGe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,iOAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAO,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;0BAOlH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,0OAAY;oCAAC,WAAU;;;;;;;;;;;0CAE1B,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCAAO,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;0BAOlH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAEpD,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;KApDwB", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/StatisticsCards.jsx"], "sourcesContent": ["\"use client\"\nimport { ArrowUpIcon, ArrowDownIcon } from \"@heroicons/react/24/solid\"\n\nexport default function StatisticsCards() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n      {/* Nombre de véhicules */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <span className=\"text-blue-600 text-sm\">📊</span>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Nombre de véhicules</p>\n            <p className=\"text-lg font-bold text-gray-900\">40,689</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowUpIcon className=\"w-3 h-3 text-green-500 mr-1\" />\n          <span>+4.5% vs mois précédent</span>\n        </div>\n      </div>\n\n      {/* Revenus générés */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center\">\n            <span className=\"text-green-600 text-sm\">💰</span>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Revenus générés</p>\n            <p className=\"text-lg font-bold text-gray-900\">589,000</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowUpIcon className=\"w-3 h-3 text-green-500 mr-1\" />\n          <span>+8.2% vs mois précédent</span>\n        </div>\n      </div>\n\n      {/* Utilisateurs actifs */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center\">\n            <span className=\"text-orange-600 text-sm\">👥</span>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Utilisateurs actifs</p>\n            <p className=\"text-lg font-bold text-gray-900\">2040</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowUpIcon className=\"w-3 h-3 text-green-500 mr-1\" />\n          <span>+2.1% vs la semaine dernière</span>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAGe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;0CAE1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qOAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAyB;;;;;;;;;;;0CAE3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qOAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;;;;;;0CAE5C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,6LAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qOAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;KAvDwB", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/ConversionChart.jsx"], "sourcesContent": ["\"use client\"\nimport { Line } from 'react-chartjs-2'\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n} from 'chart.js'\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n)\n\nexport default function ConversionChart() {\n  const data = {\n    labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    datasets: [\n      {\n        label: 'Site Conversion',\n        data: [65, 78, 85, 72, 88, 95, 82],\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 3,\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: 'rgb(59, 130, 246)',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 6,\n        pointHoverRadius: 8,\n      }\n    ]\n  }\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: false,\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: 'rgba(59, 130, 246, 0.5)',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: false,\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false,\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          }\n        }\n      },\n      y: {\n        grid: {\n          color: 'rgba(107, 114, 128, 0.1)',\n          borderDash: [5, 5],\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          },\n          callback: function(value) {\n            return value + '%'\n          }\n        },\n        min: 0,\n        max: 100,\n      }\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index',\n    },\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Nombre de conversion</h3>\n          <p className=\"text-xs text-gray-500\">Conversion rate by day, last week</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"text-xs text-blue-600 hover:text-blue-700\">Sync Recent</button>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n            <span className=\"text-xs text-gray-600\">Site Conversion</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"h-64\">\n        <Line data={data} options={options} />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAcA,wKAAO,CAAC,QAAQ,CACd,gLAAa,EACb,8KAAW,EACX,+KAAY,EACZ,8KAAW,EACX,wKAAK,EACL,0KAAO,EACP,yKAAM,EACN,yKAAM;AAGO,SAAS;IACtB,MAAM,OAAO;QACX,QAAQ;YAAC;YAAU;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QACtF,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG;gBAClC,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,sBAAsB;gBACtB,kBAAkB;gBAClB,kBAAkB;gBAClB,aAAa;gBACb,kBAAkB;YACpB;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;YACX;YACA,SAAS;gBACP,iBAAiB;gBACjB,YAAY;gBACZ,WAAW;gBACX,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,eAAe;YACjB;QACF;QACA,QAAQ;YACN,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,OAAO;oBACP,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;oBACA,UAAU,SAAS,KAAK;wBACtB,OAAO,QAAQ;oBACjB;gBACF;gBACA,KAAK;gBACL,KAAK;YACP;QACF;QACA,aAAa;YACX,WAAW;YACX,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;0CAA4C;;;;;;0CAC9D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iKAAI;oBAAC,MAAM;oBAAM,SAAS;;;;;;;;;;;;;;;;;AAInC;KAxGwB", "debugId": null}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/CoverageMap.jsx"], "sourcesContent": ["\"use client\"\n\nexport default function CoverageMap() {\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">Couverture</h3>\n      </div>\n\n      {/* Simplified map representation matching the image */}\n      <div className=\"relative h-32 bg-gray-50 rounded-lg overflow-hidden mb-3\">\n        {/* Map background with country/region shapes */}\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100\">\n          {/* Main coverage areas - simplified country/region shapes */}\n          <div className=\"absolute top-4 left-6 w-8 h-6 bg-blue-600 rounded-sm opacity-80 transform rotate-12\"></div>\n          <div className=\"absolute top-6 right-8 w-10 h-8 bg-blue-500 rounded-sm opacity-70 transform -rotate-6\"></div>\n          <div className=\"absolute bottom-6 left-4 w-6 h-4 bg-blue-700 rounded-sm opacity-90 transform rotate-45\"></div>\n          <div className=\"absolute bottom-4 right-6 w-8 h-6 bg-blue-400 rounded-sm opacity-60 transform -rotate-12\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-4 bg-blue-800 rounded-sm opacity-85\"></div>\n\n          {/* Additional smaller coverage areas */}\n          <div className=\"absolute top-3 right-4 w-3 h-3 bg-blue-600 rounded-full opacity-75\"></div>\n          <div className=\"absolute bottom-8 left-8 w-4 h-4 bg-blue-500 rounded-full opacity-80\"></div>\n          <div className=\"absolute top-10 left-1/3 w-2 h-2 bg-blue-700 rounded-full opacity-90\"></div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;0BAIpD,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;KA1BwB", "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/CriticalAlerts.jsx"], "sourcesContent": ["\"use client\"\nimport { ExclamationTriangleIcon, InformationCircleIcon } from \"@heroicons/react/24/outline\"\n\nexport default function CriticalAlerts() {\n  const alerts = [\n    {\n      id: 1,\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone Sud • 68% dispo\",\n      bgColor: \"bg-orange-50\"\n    },\n    {\n      id: 2,\n      title: \"Chauffeurs non validés\",\n      description: \"5 chauffeurs en attente\",\n      bgColor: \"bg-red-50\"\n    },\n    {\n      id: 3,\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone ouest • 52% dispo\",\n      bgColor: \"bg-orange-50\"\n    },\n    {\n      id: 4,\n      title: \"Urgent non traités\",\n      description: \"12 incidents ouverts\",\n      bgColor: \"bg-orange-50\"\n    }\n  ]\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">Alertes critiques</h3>\n        <button className=\"text-blue-600 hover:text-blue-700 text-xs\">\n          Sync Recent\n        </button>\n      </div>\n\n      <div className=\"space-y-3\">\n        {alerts.map((alert) => (\n          <div key={alert.id} className={`${alert.bgColor} rounded-lg p-3 border border-orange-200`}>\n            <div className=\"flex items-start space-x-3\">\n              <div className=\"w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                <span className=\"text-orange-600 text-xs\">⚠️</span>\n              </div>\n\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"text-xs font-medium text-gray-900 mb-1\">\n                  {alert.title}\n                </h4>\n                <p className=\"text-xs text-gray-600\">\n                  {alert.description}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAGe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,SAAS;QACX;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,6LAAC;wBAAO,WAAU;kCAA4C;;;;;;;;;;;;0BAKhE,6LAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;wBAAmB,WAAW,AAAC,GAAgB,OAAd,MAAM,OAAO,EAAC;kCAC9C,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA0B;;;;;;;;;;;8CAG5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;uBAXhB,MAAM,EAAE;;;;;;;;;;;;;;;;AAoB5B;KA3DwB", "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/AvailabilityTable.jsx"], "sourcesContent": ["\"use client\"\n\nexport default function AvailabilityTable() {\n  const availabilityData = [\n    {\n      zone: \"Zone 1\",\n      availability: 85,\n      status: \"Disponible\",\n      vehicles: 24,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 2\", \n      availability: 68,\n      status: \"Disponible\",\n      vehicles: 18,\n      color: \"bg-yellow-500\"\n    },\n    {\n      zone: \"Zone 3\",\n      availability: 92,\n      status: \"Disponible\", \n      vehicles: 31,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 4\",\n      availability: 45,\n      status: \"Critique\",\n      vehicles: 12,\n      color: \"bg-red-500\"\n    },\n    {\n      zone: \"Zone 5\",\n      availability: 78,\n      status: \"Disponible\",\n      vehicles: 22,\n      color: \"bg-green-500\"\n    }\n  ]\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case \"Disponible\":\n        return \"text-green-600 bg-green-100\"\n      case \"Critique\":\n        return \"text-red-600 bg-red-100\"\n      default:\n        return \"text-gray-600 bg-gray-100\"\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">Flotte disponible par zone</h3>\n      </div>\n\n      <div className=\"space-y-3\">\n        <div className=\"grid grid-cols-4 gap-4 text-xs text-gray-500 pb-2 border-b border-gray-100\">\n          <span>Zone</span>\n          <span>Disponibilité</span>\n          <span>Statut</span>\n          <span>Progression</span>\n        </div>\n\n        {availabilityData.map((item, index) => (\n          <div key={index} className=\"grid grid-cols-4 gap-4 items-center py-2\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${item.color}`}></div>\n              <span className=\"text-xs font-medium text-gray-900\">{item.zone}</span>\n            </div>\n\n            <div className=\"text-xs text-gray-600\">\n              {item.availability}%\n            </div>\n\n            <div>\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>\n                {item.status}\n              </span>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex-1 bg-gray-200 rounded-full h-1.5\">\n                <div\n                  className={`h-1.5 rounded-full ${item.color} transition-all duration-300`}\n                  style={{ width: `${item.availability}%` }}\n                ></div>\n              </div>\n              <span className=\"text-xs text-gray-500 min-w-[2rem]\">{item.availability}%</span>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;0BAGpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;oBAGP,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,6LAAC;4BAAgB,WAAU;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,AAAC,wBAAkC,OAAX,KAAK,KAAK;;;;;;sDAClD,6LAAC;4CAAK,WAAU;sDAAqC,KAAK,IAAI;;;;;;;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;;wCACZ,KAAK,YAAY;wCAAC;;;;;;;8CAGrB,6LAAC;8CACC,cAAA,6LAAC;wCAAK,WAAW,AAAC,8CAAyE,OAA5B,eAAe,KAAK,MAAM;kDACtF,KAAK,MAAM;;;;;;;;;;;8CAIhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,AAAC,sBAAgC,OAAX,KAAK,KAAK,EAAC;gDAC5C,OAAO;oDAAE,OAAO,AAAC,GAAoB,OAAlB,KAAK,YAAY,EAAC;gDAAG;;;;;;;;;;;sDAG5C,6LAAC;4CAAK,WAAU;;gDAAsC,KAAK,YAAY;gDAAC;;;;;;;;;;;;;;2BAvBlE;;;;;;;;;;;;;;;;;AA8BpB;KA/FwB", "debugId": null}}]}