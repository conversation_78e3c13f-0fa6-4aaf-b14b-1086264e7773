"use client"
import { ExclamationTriangleIcon, InformationCircleIcon } from "@heroicons/react/24/outline"

export default function CriticalAlerts() {
  const alerts = [
    {
      id: 1,
      type: "warning",
      title: "Flotte disponible insuffisante",
      description: "Zone Sud • 68% dispo",
      time: "Il y a 2h",
      icon: ExclamationTriangleIcon,
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600",
      borderColor: "border-orange-200"
    },
    {
      id: 2,
      type: "error",
      title: "Chauffeurs non validés",
      description: "5 chauffeurs en attente",
      time: "Il y a 3h",
      icon: ExclamationTriangleIcon,
      bgColor: "bg-red-50",
      iconColor: "text-red-600",
      borderColor: "border-red-200"
    },
    {
      id: 3,
      type: "warning",
      title: "Flotte disponible insuffisante",
      description: "Zone ouest • 52% dispo",
      time: "Il y a 4h",
      icon: ExclamationTriangleIcon,
      bgColor: "bg-orange-50",
      iconColor: "text-orange-600",
      borderColor: "border-orange-200"
    },
    {
      id: 4,
      type: "info",
      title: "Urgent non traités",
      description: "12 incidents ouverts",
      time: "Il y a 5h",
      icon: InformationCircleIcon,
      bgColor: "bg-blue-50",
      iconColor: "text-blue-600",
      borderColor: "border-blue-200"
    }
  ]

  return (
    <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Alertes critiques</h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          Sync Recent
        </button>
      </div>
      
      <div className="space-y-4">
        {alerts.map((alert) => {
          const Icon = alert.icon
          return (
            <div 
              key={alert.id} 
              className={`${alert.bgColor} ${alert.borderColor} border rounded-lg p-4 transition-all duration-200 hover:shadow-sm`}
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg ${alert.bgColor}`}>
                  <Icon className={`w-5 h-5 ${alert.iconColor}`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm font-medium text-gray-900 mb-1">
                    {alert.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">
                    {alert.description}
                  </p>
                  <p className="text-xs text-gray-500">
                    {alert.time}
                  </p>
                </div>
                
                <button className="text-gray-400 hover:text-gray-600">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          )
        })}
      </div>
      
      <div className="mt-6 pt-4 border-t border-gray-100">
        <button className="w-full text-center text-blue-600 hover:text-blue-700 text-sm font-medium py-2">
          Voir toutes les alertes
        </button>
      </div>
    </div>
  )
}
