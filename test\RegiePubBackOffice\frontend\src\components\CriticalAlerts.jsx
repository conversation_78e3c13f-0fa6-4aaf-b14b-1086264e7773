"use client"
import { ExclamationTriangleIcon, InformationCircleIcon } from "@heroicons/react/24/outline"

export default function CriticalAlerts() {
  const alerts = [
    {
      id: 1,
      title: "Flotte disponible insuffisante",
      description: "Zone Sud • 68% dispo",
      bgColor: "bg-orange-50"
    },
    {
      id: 2,
      title: "Chauffeurs non validés",
      description: "5 chauffeurs en attente",
      bgColor: "bg-red-50"
    },
    {
      id: 3,
      title: "Flotte disponible insuffisante",
      description: "Zone ouest • 52% dispo",
      bgColor: "bg-orange-50"
    },
    {
      id: 4,
      title: "Urgent non traités",
      description: "12 incidents ouverts",
      bgColor: "bg-orange-50"
    }
  ]

  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-900">Alertes critiques</h3>
        <button className="text-blue-600 hover:text-blue-700 text-xs">
          Sync Recent
        </button>
      </div>

      <div className="space-y-3">
        {alerts.map((alert) => (
          <div key={alert.id} className={`${alert.bgColor} rounded-lg p-3 border border-orange-200`}>
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-orange-600 text-xs">⚠️</span>
              </div>

              <div className="flex-1 min-w-0">
                <h4 className="text-xs font-medium text-gray-900 mb-1">
                  {alert.title}
                </h4>
                <p className="text-xs text-gray-600">
                  {alert.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
